#%% md
### 1.会遇场景轨迹提取
#%%
"""
加载crossing_avoidance_scenes.pkl和overtaking_avoidance_scenes.pkl
提取每种场景中会遇船舶在会遇时间范围内的轨迹

数据结构说明：
- 每个避让场景：[机动船信息, 非机动船信息]
- 船舶信息字典：{'mmsi': int, 'lon': float, 'lat': float, 'cog': float, 'sog': float,
                'length': float, 'width': float, 'time_point': int}
"""

import pickle
import pandas as pd
import numpy as np
from pathlib import Path
from collections import defaultdict
from tqdm import tqdm


class EncounterTrajectoryExtractor:
    """会遇场景轨迹提取器"""

    def __init__(self, data_time='2024_1'):
        """
        初始化轨迹提取器

        Args:
            data_time: 数据时间标识，如 '2024_1', '2024_2', '2024_3'
        """
        self.data_time = data_time
        self.crossing_scenes = []
        self.overtaking_scenes = []
        self.tras_df = None

        # 加载基础数据
        self._load_basic_data()

    def _load_basic_data(self):
        """加载基础轨迹数据"""
        print(f"正在加载 {self.data_time} 的基础数据...")

        # 加载轨迹数据
        tras_file = f'data/tras_{self.data_time}_inter.parquet'
        if Path(tras_file).exists():
            self.tras_df = pd.read_parquet(tras_file)
            print(f"✅ 已加载轨迹数据: {len(self.tras_df)} 条记录")
        else:
            raise FileNotFoundError(f"未找到轨迹数据文件: {tras_file}")

    def load_avoidance_scenes(self):
        """加载避让场景数据"""
        print(f"正在加载 {self.data_time} 的避让场景...")

        # 加载交叉避让场景
        crossing_file = f'result/{self.data_time}/crossing_avoidance_scenes.pkl'
        if Path(crossing_file).exists():
            with open(crossing_file, 'rb') as f:
                self.crossing_scenes = pickle.load(f)
            print(f"✅ 已加载交叉避让场景: {len(self.crossing_scenes)} 个")
        else:
            print(f"⚠️  未找到交叉避让场景文件: {crossing_file}")

        # 加载追越避让场景
        overtaking_file = f'result/{self.data_time}/overtaking_avoidance_scenes.pkl'
        if Path(overtaking_file).exists():
            with open(overtaking_file, 'rb') as f:
                self.overtaking_scenes = pickle.load(f)
            print(f"✅ 已加载追越避让场景: {len(self.overtaking_scenes)} 个")
        else:
            print(f"⚠️  未找到追越避让场景文件: {overtaking_file}")

    def extract_encounter_trajectories(self, time_window_minutes=5):
        """
        提取会遇船舶在会遇时间范围内的轨迹

        Args:
            time_window_minutes: 时间窗口大小（分钟），在会遇时刻前后各扩展的时间

        Returns:
            dict: 包含交叉和追越场景轨迹的字典
        """
        print(f"正在提取会遇轨迹（时间窗口: ±{time_window_minutes}分钟）...")

        # 时间窗口转换为秒
        time_window_seconds = time_window_minutes * 60

        results = {
            'crossing_trajectories': [],
            'overtaking_trajectories': []
        }

        # 处理交叉避让场景
        if self.crossing_scenes:
            print("处理交叉避让场景...")
            crossing_trajectories = self._extract_trajectories_from_scenes(
                self.crossing_scenes, 'crossing', time_window_seconds
            )
            results['crossing_trajectories'] = crossing_trajectories

        # 处理追越避让场景
        if self.overtaking_scenes:
            print("处理追越避让场景...")
            overtaking_trajectories = self._extract_trajectories_from_scenes(
                self.overtaking_scenes, 'overtaking', time_window_seconds
            )
            results['overtaking_trajectories'] = overtaking_trajectories

        return results

    def _extract_trajectories_from_scenes(self, scenes, scene_type, time_window_seconds):
        """
        从场景列表中提取轨迹

        Args:
            scenes: 场景列表
            scene_type: 场景类型 ('crossing' 或 'overtaking')
            time_window_seconds: 时间窗口（秒）

        Returns:
            list: 轨迹数据列表
        """
        trajectories = []

        for i, scene in enumerate(tqdm(scenes, desc=f"提取{scene_type}轨迹")):
            try:
                # 解析场景数据
                maneuvering_ship = scene[0]  # 机动船信息
                other_ship = scene[1]  # 非机动船信息

                encounter_time = maneuvering_ship['time_point']

                # 计算时间范围
                start_time = encounter_time - time_window_seconds
                end_time = encounter_time + time_window_seconds

                # 提取两船的轨迹
                maneuvering_trajectory = self._get_ship_trajectory_in_timerange(
                    maneuvering_ship['mmsi'], start_time, end_time
                )
                other_trajectory = self._get_ship_trajectory_in_timerange(
                    other_ship['mmsi'], start_time, end_time
                )

                # 构建轨迹数据
                trajectory_data = {
                    'scene_id': f"{scene_type}_{i}",
                    'scene_type': scene_type,
                    'encounter_time': encounter_time,
                    'time_window': {
                        'start': start_time,
                        'end': end_time
                    },
                    'maneuvering_ship': {
                        'mmsi': maneuvering_ship['mmsi'],
                        'encounter_state': maneuvering_ship,
                        'trajectory': maneuvering_trajectory
                    },
                    'other_ship': {
                        'mmsi': other_ship['mmsi'],
                        'encounter_state': other_ship,
                        'trajectory': other_trajectory
                    }
                }

                trajectories.append(trajectory_data)

            except Exception as e:
                print(f"⚠️  处理场景 {i} 时出错: {e}")
                continue

        return trajectories

    def _get_ship_trajectory_in_timerange(self, mmsi, start_time, end_time):
        """
        获取指定船舶在时间范围内的轨迹

        Args:
            mmsi: 船舶MMSI
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            pd.DataFrame: 轨迹数据
        """
        # 筛选指定船舶和时间范围的数据
        trajectory = self.tras_df[
            (self.tras_df['MMSI'] == mmsi) &
            (self.tras_df['PosTime'] >= start_time) &
            (self.tras_df['PosTime'] <= end_time)
            ].copy()

        # 按时间排序
        trajectory = trajectory.sort_values('PosTime')

        return trajectory

    def save_trajectories(self, trajectories, output_dir='result/trajectories'):
        """
        保存提取的轨迹数据

        Args:
            trajectories: 轨迹数据字典
            output_dir: 输出目录
        """
        output_path = Path(output_dir) / self.data_time
        output_path.mkdir(parents=True, exist_ok=True)

        # 保存交叉避让轨迹
        if trajectories['crossing_trajectories']:
            crossing_file = output_path / 'crossing_encounter_trajectories.pkl'
            with open(crossing_file, 'wb') as f:
                pickle.dump(trajectories['crossing_trajectories'], f)
            print(f"✅ 交叉避让轨迹已保存: {crossing_file}")
            print(f"   包含 {len(trajectories['crossing_trajectories'])} 个场景")

        # 保存追越避让轨迹
        if trajectories['overtaking_trajectories']:
            overtaking_file = output_path / 'overtaking_encounter_trajectories.pkl'
            with open(overtaking_file, 'wb') as f:
                pickle.dump(trajectories['overtaking_trajectories'], f)
            print(f"✅ 追越避让轨迹已保存: {overtaking_file}")
            print(f"   包含 {len(trajectories['overtaking_trajectories'])} 个场景")

    def print_summary(self, trajectories):
        """打印提取结果摘要"""
        print(f"\n📊 轨迹提取摘要 ({self.data_time}):")
        print("=" * 50)

        crossing_count = len(trajectories['crossing_trajectories'])
        overtaking_count = len(trajectories['overtaking_trajectories'])
        total_count = crossing_count + overtaking_count

        print(f"总场景数: {total_count}")
        print(f"├─ 交叉避让场景: {crossing_count}")
        print(f"└─ 追越避让场景: {overtaking_count}")

        # 统计轨迹点数
        if crossing_count > 0:
            crossing_points = sum(
                len(traj['maneuvering_ship']['trajectory']) + len(traj['other_ship']['trajectory'])
                for traj in trajectories['crossing_trajectories']
            )
            print(f"\n交叉避让轨迹点数: {crossing_points}")

        if overtaking_count > 0:
            overtaking_points = sum(
                len(traj['maneuvering_ship']['trajectory']) + len(traj['other_ship']['trajectory'])
                for traj in trajectories['overtaking_trajectories']
            )
            print(f"追越避让轨迹点数: {overtaking_points}")


def main():
    """主函数"""
    # 可以处理多个月份的数据
    data_times = ['2024_1', '2024_2', '2024_3']

    for data_time in data_times:
        print(f"\n{'=' * 60}")
        print(f"处理 {data_time} 数据")
        print(f"{'=' * 60}")

        try:
            # 创建提取器
            extractor = EncounterTrajectoryExtractor(data_time)

            # 加载避让场景
            extractor.load_avoidance_scenes()

            # 提取轨迹（时间窗口可调整）
            trajectories = extractor.extract_encounter_trajectories(time_window_minutes=10)

            # 保存结果
            extractor.save_trajectories(trajectories)

            # 打印摘要
            extractor.print_summary(trajectories)

        except Exception as e:
            print(f"❌ 处理 {data_time} 时出错: {e}")
            continue


if __name__ == '__main__':
    main()

#%% md
### 2.批量冲突计算
#%%
import pickle

with open('result/trajectories/2024_1/crossing_encounter_trajectories.pkl', 'rb') as f:
    crossing_trajectories = pickle.load(f)
with open('result/trajectories/2024_1/overtaking_encounter_trajectories.pkl', 'rb') as f:
    overtaking_trajectories = pickle.load(f)
#%%
import pandas as pd
import numpy as np
from shapely.geometry import Point
from shapely.affinity import scale, rotate, translate
from tqdm import tqdm
from methods.trans import Trans

trans = Trans()


def overlap_ratios_shapely(m1, n1, A1, B1, phi1,
                           m2, n2, A2, B2, phi2,
                           circle_resolution=256):
    """
    计算两椭圆交集面积分别占各自面积的比值，使用 Shapely。

    参数:
      - m1,n1,A1,B1,phi1: 椭圆1 的中心、长短半轴及旋转角（度）
      - m2,n2,A2,B2,phi2: 椭圆2 同上
      - circle_resolution: 用于近似圆的分段数，越大结果越精确

    返回:
      (ratio1, ratio2)，其中
        ratio1 = area(intersection) / (π A1 B1)
        ratio2 = area(intersection) / (π A2 B2)
    """
    # 构造单位圆
    unit_circle = Point(0, 0).buffer(1, resolution=circle_resolution)

    # 椭圆1：先缩放，再旋转，最后平移
    e1 = scale(unit_circle, A1, B1)
    e1 = rotate(e1, phi1, origin=(0, 0), use_radians=False)
    e1 = translate(e1, m1, n1)

    # 椭圆2
    e2 = scale(unit_circle, A2, B2)
    e2 = rotate(e2, phi2, origin=(0, 0), use_radians=False)
    e2 = translate(e2, m2, n2)

    # 求交集
    inter = e1.intersection(e2)
    area_inter = inter.area

    # 各自面积
    area1 = np.pi * A1 * B1
    area2 = np.pi * A2 * B2

    return max(area_inter / area1, area_inter / area2)


def get_ab(scence_type, length):
    if scence_type == 'crossing':
        if length <= 100:
            return 271, 192
        else:
            return 375, 210
    else:
        if length <= 100:
            return 180, 85
        else:
            return 290, 120


def conflict_detection(trajectories, scene_type='crossing'):
    Conflicts = []
    for tras in tqdm(trajectories):
        df1 = tras['maneuvering_ship']['trajectory']
        df2 = tras['other_ship']['trajectory']

        a1, b1 = get_ab(scene_type, df1['Length'].values[0])
        a2, b2 = get_ab(scene_type, df2['Length'].values[0])

        common_times = np.intersect1d(df1['PosTime'].values, df2['PosTime'].values)
        df1_aligned = df1[df1['PosTime'].isin(common_times)].reset_index(drop=True)
        df2_aligned = df2[df2['PosTime'].isin(common_times)].reset_index(drop=True)

        X1, Y1 = trans.LonLat2Gauss(df1_aligned['Lon'].values, df1_aligned['Lat'].values)
        X2, Y2 = trans.LonLat2Gauss(df2_aligned['Lon'].values, df2_aligned['Lat'].values)

        Cog1 = df1_aligned['Cog'].values
        Cog2 = df2_aligned['Cog'].values

        conflicts = [overlap_ratios_shapely(X1[i], Y1[i], a1, b1, Cog1[i],
                                            X2[i], Y2[i], a2, b2, Cog2[i])
                     for i in range(len(common_times))]
        Conflicts.append(pd.DataFrame({'PosTime': common_times, 'Conflict': conflicts}))
    return Conflicts


Conflicts_crossing = conflict_detection(crossing_trajectories, scene_type='crossing')
Conflicts_overtaking = conflict_detection(overtaking_trajectories, scene_type='overtaking')

#%% md
### 3.冲突趋势检测
#%%
# 简化版本：只筛选符合条件的轨迹索引
def filter_pattern_trajectories(conflicts_list, min_conflict_threshold=0.01):
    """
    筛选符合条件的轨迹：冲突先逐渐增大，到最大值，然后逐渐减小

    返回:
    - 符合条件的轨迹索引列表
    """
    valid_indices = []

    for traj_idx, conflict_df in enumerate(conflicts_list):
        if len(conflict_df) < 5:  # 至少需要5个点
            continue

        conflicts = conflict_df['Conflict'].values

        # 过滤掉过小的冲突值
        valid_mask = conflicts >= min_conflict_threshold
        if np.sum(valid_mask) < 5:
            continue

        valid_conflicts = conflicts[valid_mask]

        # 找到最大值位置
        max_idx = np.argmax(valid_conflicts)

        # 最大值不能在两端
        if max_idx == 0 or max_idx == len(valid_conflicts) - 1:
            continue

        # 检查上升段和下降段
        rising_phase = valid_conflicts[:max_idx+1]
        falling_phase = valid_conflicts[max_idx:]

        # 检查上升趋势：至少60%的点呈上升
        rising_diffs = np.diff(rising_phase)
        rising_ratio = np.sum(rising_diffs > 0) / len(rising_diffs) if len(rising_diffs) > 0 else 0

        # 检查下降趋势：至少60%的点呈下降
        falling_diffs = np.diff(falling_phase)
        falling_ratio = np.sum(falling_diffs < 0) / len(falling_diffs) if len(falling_diffs) > 0 else 0

        # 符合条件：上升趋势明显 且 下降趋势明显
        if rising_ratio >= 0.3 and falling_ratio >= 0.3:
            valid_indices.append(traj_idx)

    return valid_indices

# 筛选符合条件的轨迹
crossing_valid_indices = filter_pattern_trajectories(Conflicts_crossing)
overtaking_valid_indices = filter_pattern_trajectories(Conflicts_overtaking)

# print(f"\n=== 简化筛选结果 ===")
# print(f"交叉场景符合条件的轨迹索引: {crossing_valid_indices}")
# print(f"追越场景符合条件的轨迹索引: {overtaking_valid_indices}")

print(f"\n交叉场景: {len(crossing_valid_indices)}/{len(Conflicts_crossing)} 个轨迹符合条件")
print(f"追越场景: {len(overtaking_valid_indices)}/{len(Conflicts_overtaking)} 个轨迹符合条件")

#%%
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation
import numpy as np

def create_ellipse(x, y, a, b, angle):
    """创建椭圆"""
    return patches.Ellipse((x, y), 2*a, 2*b, angle=angle, alpha=0.3)

def visualize_conflict_trajectory(traj_idx, trajectories, conflicts, scene_type='crossing'):
    """
    可视化单个轨迹的冲突过程

    参数:
    - traj_idx: 轨迹索引
    - trajectories: 原始轨迹数据
    - conflicts: 冲突数据
    - scene_type: 场景类型
    """
    # 获取轨迹数据
    traj = trajectories[traj_idx]
    conflict_df = conflicts[traj_idx]

    df1 = traj['maneuvering_ship']['trajectory']
    df2 = traj['other_ship']['trajectory']

    # 获取椭圆参数
    a1, b1 = get_ab(scene_type, df1['Length'].values[0])
    a2, b2 = get_ab(scene_type, df2['Length'].values[0])

    # 对齐时间
    common_times = np.intersect1d(df1['PosTime'].values, df2['PosTime'].values)
    df1_aligned = df1[df1['PosTime'].isin(common_times)].reset_index(drop=True)
    df2_aligned = df2[df2['PosTime'].isin(common_times)].reset_index(drop=True)

    # 坐标转换
    X1, Y1 = trans.LonLat2Gauss(df1_aligned['Lon'].values, df1_aligned['Lat'].values)
    X2, Y2 = trans.LonLat2Gauss(df2_aligned['Lon'].values, df2_aligned['Lat'].values)

    Cog1 = df1_aligned['Cog'].values
    Cog2 = df2_aligned['Cog'].values

    conflicts_values = conflict_df['Conflict'].values

    # 创建静态图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # 左图：轨迹总览
    ax1.plot(X1, Y1, 'b-', linewidth=2, label='本船轨迹')
    ax1.plot(X2, Y2, 'r-', linewidth=2, label='它船轨迹')
    ax1.scatter(X1[0], Y1[0], c='blue', s=100, marker='o', label='本船起点')
    ax1.scatter(X2[0], Y2[0], c='red', s=100, marker='o', label='它船起点')
    ax1.scatter(X1[-1], Y1[-1], c='blue', s=100, marker='s', label='本船终点')
    ax1.scatter(X2[-1], Y2[-1], c='red', s=100, marker='s', label='它船终点')

    # 找到最大冲突时刻
    max_conflict_idx = np.argmax(conflicts_values)
    ax1.scatter(X1[max_conflict_idx], Y1[max_conflict_idx], c='orange', s=200, marker='*', label='最大冲突点')
    ax1.scatter(X2[max_conflict_idx], Y2[max_conflict_idx], c='orange', s=200, marker='*')

    ax1.set_xlabel('X (米)')
    ax1.set_ylabel('Y (米)')
    ax1.set_title(f'{scene_type} 场景轨迹 {traj_idx}')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.axis('equal')

    # 右图：冲突值变化
    time_relative = (common_times - common_times[0]) / 60  # 转换为分钟
    ax2.plot(time_relative, conflicts_values, 'g-', linewidth=2)
    ax2.scatter(time_relative[max_conflict_idx], conflicts_values[max_conflict_idx],
               c='orange', s=100, marker='*', label='最大冲突')
    ax2.set_xlabel('时间 (分钟)')
    ax2.set_ylabel('冲突值')
    ax2.set_title('冲突值变化')
    ax2.grid(True, alpha=0.3)
    ax2.legend()

    plt.tight_layout()
    plt.show()

    return X1, Y1, X2, Y2, Cog1, Cog2, conflicts_values, a1, b1, a2, b2

def create_animation(traj_idx, trajectories, conflicts, scene_type='crossing', save_gif=False):
    """
    创建冲突过程动画

    参数:
    - traj_idx: 轨迹索引
    - trajectories: 原始轨迹数据
    - conflicts: 冲突数据
    - scene_type: 场景类型
    - save_gif: 是否保存为GIF
    """
    # 获取数据
    X1, Y1, X2, Y2, Cog1, Cog2, conflicts_values, a1, b1, a2, b2 = visualize_conflict_trajectory(
        traj_idx, trajectories, conflicts, scene_type)

    # 创建动画图
    fig, ax = plt.subplots(figsize=(10, 8))

    # 设置坐标范围
    all_x = np.concatenate([X1, X2])
    all_y = np.concatenate([Y1, Y2])
    margin = max(a1, a2, b1, b2) * 2
    ax.set_xlim(min(all_x) - margin, max(all_x) + margin)
    ax.set_ylim(min(all_y) - margin, max(all_y) + margin)

    # 绘制完整轨迹（淡色）
    ax.plot(X1, Y1, 'b-', alpha=0.3, linewidth=1, label='本船轨迹')
    ax.plot(X2, Y2, 'r-', alpha=0.3, linewidth=1, label='它船轨迹')

    # 初始化动画元素
    ship1_point, = ax.plot([], [], 'bo', markersize=8, label='本船')
    ship2_point, = ax.plot([], [], 'ro', markersize=8, label='它船')
    ship1_trail, = ax.plot([], [], 'b-', linewidth=2, alpha=0.7)
    ship2_trail, = ax.plot([], [], 'r-', linewidth=2, alpha=0.7)

    # 椭圆
    ellipse1 = patches.Ellipse((0, 0), 2*a1, 2*b1, alpha=0.3, color='blue')
    ellipse2 = patches.Ellipse((0, 0), 2*a2, 2*b2, alpha=0.3, color='red')
    ax.add_patch(ellipse1)
    ax.add_patch(ellipse2)

    # 冲突值文本
    conflict_text = ax.text(0.02, 0.98, '', transform=ax.transAxes,
                           fontsize=12, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    ax.set_xlabel('X (米)')
    ax.set_ylabel('Y (米)')
    ax.set_title(f'{scene_type} 场景冲突动画 - 轨迹 {traj_idx}')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.axis('equal')

    def animate(frame):
        if frame >= len(X1):
            return ship1_point, ship2_point, ship1_trail, ship2_trail, ellipse1, ellipse2, conflict_text

        # 更新船舶位置
        ship1_point.set_data([X1[frame]], [Y1[frame]])
        ship2_point.set_data([X2[frame]], [Y2[frame]])

        # 更新轨迹
        ship1_trail.set_data(X1[:frame+1], Y1[:frame+1])
        ship2_trail.set_data(X2[:frame+1], Y2[:frame+1])

        # 更新椭圆
        ellipse1.center = (X1[frame], Y1[frame])
        ellipse1.angle = Cog1[frame]
        ellipse2.center = (X2[frame], Y2[frame])
        ellipse2.angle = Cog2[frame]

        # 更新冲突值
        conflict_text.set_text(f'时间步: {frame+1}/{len(X1)}\n冲突值: {conflicts_values[frame]:.4f}')

        return ship1_point, ship2_point, ship1_trail, ship2_trail, ellipse1, ellipse2, conflict_text

    # 创建动画
    anim = FuncAnimation(fig, animate, frames=len(X1), interval=200, blit=True, repeat=True)

    if save_gif:
        anim.save(f'conflict_animation_{scene_type}_{traj_idx}.gif', writer='pillow', fps=5)
        print(f"动画已保存为: conflict_animation_{scene_type}_{traj_idx}.gif")

    plt.show()
    return anim

# 可视化符合条件的轨迹
if len(crossing_valid_indices) > 0:
    print("可视化交叉场景符合条件的轨迹...")
    # 选择第一个符合条件的轨迹进行可视化
    idx = crossing_valid_indices[0]
    print(f"正在可视化交叉场景轨迹 {idx}")

    # 静态图
    visualize_conflict_trajectory(idx, crossing_trajectories, Conflicts_crossing, 'crossing')

    # 动画（可选择保存为GIF）
    anim = create_animation(idx, crossing_trajectories, Conflicts_crossing, 'crossing', save_gif=False)

def batch_visualize(valid_indices, trajectories, conflicts, scene_type, max_plots=4):
    """
    批量可视化多个轨迹

    参数:
    - valid_indices: 符合条件的轨迹索引列表
    - trajectories: 轨迹数据
    - conflicts: 冲突数据
    - scene_type: 场景类型
    - max_plots: 最大绘图数量
    """
    if len(valid_indices) == 0:
        print(f"没有{scene_type}场景的符合条件轨迹")
        return

    n_plots = min(len(valid_indices), max_plots)
    cols = 2
    rows = (n_plots + 1) // 2

    fig, axes = plt.subplots(rows, cols, figsize=(15, 7*rows))
    if rows == 1:
        axes = axes.reshape(1, -1)
    elif n_plots == 1:
        axes = axes.reshape(1, 1)

    fig.suptitle(f'{scene_type} 场景符合条件的轨迹概览', fontsize=16, fontweight='bold')

    for i in range(n_plots):
        row = i // cols
        col = i % cols
        ax = axes[row, col] if rows > 1 else axes[col]

        traj_idx = valid_indices[i]

        # 获取数据
        traj = trajectories[traj_idx]
        conflict_df = conflicts[traj_idx]

        df1 = traj['maneuvering_ship']['trajectory']
        df2 = traj['other_ship']['trajectory']

        # 坐标转换
        common_times = np.intersect1d(df1['PosTime'].values, df2['PosTime'].values)
        df1_aligned = df1[df1['PosTime'].isin(common_times)].reset_index(drop=True)
        df2_aligned = df2[df2['PosTime'].isin(common_times)].reset_index(drop=True)

        X1, Y1 = trans.LonLat2Gauss(df1_aligned['Lon'].values, df1_aligned['Lat'].values)
        X2, Y2 = trans.LonLat2Gauss(df2_aligned['Lon'].values, df2_aligned['Lat'].values)

        conflicts_values = conflict_df['Conflict'].values
        max_conflict_idx = np.argmax(conflicts_values)

        # 绘制轨迹
        ax.plot(X1, Y1, 'b-', linewidth=2, label='本船')
        ax.plot(X2, Y2, 'r-', linewidth=2, label='它船')
        ax.scatter(X1[max_conflict_idx], Y1[max_conflict_idx], c='orange', s=100, marker='*')
        ax.scatter(X2[max_conflict_idx], Y2[max_conflict_idx], c='orange', s=100, marker='*')

        ax.set_title(f'轨迹 {traj_idx}\n最大冲突: {max(conflicts_values):.4f}')
        ax.set_xlabel('X (米)')
        ax.set_ylabel('Y (米)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axis('equal')

    # 隐藏多余的子图
    for i in range(n_plots, rows * cols):
        row = i // cols
        col = i % cols
        if rows > 1:
            axes[row, col].set_visible(False)
        else:
            axes[col].set_visible(False)

    plt.tight_layout()
    plt.show()

# 批量可视化
print("\n=== 批量可视化符合条件的轨迹 ===")
if len(crossing_valid_indices) > 0:
    print(f"交叉场景符合条件的轨迹: {len(crossing_valid_indices)} 个")
    batch_visualize(crossing_valid_indices, crossing_trajectories, Conflicts_crossing, '交叉')

if len(overtaking_valid_indices) > 0:
    print(f"追越场景符合条件的轨迹: {len(overtaking_valid_indices)} 个")
    batch_visualize(overtaking_valid_indices, overtaking_trajectories, Conflicts_overtaking, '追越')

#%%
# 交互式选择轨迹进行动画展示
def interactive_animation():
    """交互式选择轨迹进行动画展示"""
    print("\n=== 交互式动画展示 ===")

    if len(crossing_valid_indices) > 0:
        print(f"交叉场景符合条件的轨迹索引: {crossing_valid_indices}")
        choice = input("请输入要查看动画的交叉场景轨迹索引（回车跳过）: ")
        if choice.strip() and choice.strip().isdigit():
            idx = int(choice.strip())
            if idx in crossing_valid_indices:
                print(f"正在生成交叉场景轨迹 {idx} 的动画...")
                anim = create_animation(idx, crossing_trajectories, Conflicts_crossing, 'crossing')
            else:
                print("输入的索引不在符合条件的列表中")

    if len(overtaking_valid_indices) > 0:
        print(f"追越场景符合条件的轨迹索引: {overtaking_valid_indices}")
        choice = input("请输入要查看动画的追越场景轨迹索引（回车跳过）: ")
        if choice.strip() and choice.strip().isdigit():
            idx = int(choice.strip())
            if idx in overtaking_valid_indices:
                print(f"正在生成追越场景轨迹 {idx} 的动画...")
                anim = create_animation(idx, overtaking_trajectories, Conflicts_overtaking, 'overtaking')
            else:
                print("输入的索引不在符合条件的列表中")

# 运行交互式动画（可选）
# interactive_animation()

#%%
crossing_valid_indices
#%%
