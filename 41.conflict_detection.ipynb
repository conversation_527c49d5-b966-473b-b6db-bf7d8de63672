#%% md
### 1.会遇场景轨迹提取
#%%
"""
加载crossing_avoidance_scenes.pkl和overtaking_avoidance_scenes.pkl
提取每种场景中会遇船舶在会遇时间范围内的轨迹

数据结构说明：
- 每个避让场景：[机动船信息, 非机动船信息]
- 船舶信息字典：{'mmsi': int, 'lon': float, 'lat': float, 'cog': float, 'sog': float,
                'length': float, 'width': float, 'time_point': int}
"""

import pickle
import pandas as pd
import numpy as np
from pathlib import Path
from collections import defaultdict
from tqdm import tqdm


class EncounterTrajectoryExtractor:
    """会遇场景轨迹提取器"""

    def __init__(self, data_time='2024_1'):
        """
        初始化轨迹提取器

        Args:
            data_time: 数据时间标识，如 '2024_1', '2024_2', '2024_3'
        """
        self.data_time = data_time
        self.crossing_scenes = []
        self.overtaking_scenes = []
        self.tras_df = None

        # 加载基础数据
        self._load_basic_data()

    def _load_basic_data(self):
        """加载基础轨迹数据"""
        print(f"正在加载 {self.data_time} 的基础数据...")

        # 加载轨迹数据
        tras_file = f'data/tras_{self.data_time}_inter.parquet'
        if Path(tras_file).exists():
            self.tras_df = pd.read_parquet(tras_file)
            print(f"✅ 已加载轨迹数据: {len(self.tras_df)} 条记录")
        else:
            raise FileNotFoundError(f"未找到轨迹数据文件: {tras_file}")

    def load_avoidance_scenes(self):
        """加载避让场景数据"""
        print(f"正在加载 {self.data_time} 的避让场景...")

        # 加载交叉避让场景
        crossing_file = f'result/{self.data_time}/crossing_avoidance_scenes.pkl'
        if Path(crossing_file).exists():
            with open(crossing_file, 'rb') as f:
                self.crossing_scenes = pickle.load(f)
            print(f"✅ 已加载交叉避让场景: {len(self.crossing_scenes)} 个")
        else:
            print(f"⚠️  未找到交叉避让场景文件: {crossing_file}")

        # 加载追越避让场景
        overtaking_file = f'result/{self.data_time}/overtaking_avoidance_scenes.pkl'
        if Path(overtaking_file).exists():
            with open(overtaking_file, 'rb') as f:
                self.overtaking_scenes = pickle.load(f)
            print(f"✅ 已加载追越避让场景: {len(self.overtaking_scenes)} 个")
        else:
            print(f"⚠️  未找到追越避让场景文件: {overtaking_file}")

    def extract_encounter_trajectories(self, time_window_minutes=5):
        """
        提取会遇船舶在会遇时间范围内的轨迹

        Args:
            time_window_minutes: 时间窗口大小（分钟），在会遇时刻前后各扩展的时间

        Returns:
            dict: 包含交叉和追越场景轨迹的字典
        """
        print(f"正在提取会遇轨迹（时间窗口: ±{time_window_minutes}分钟）...")

        # 时间窗口转换为秒
        time_window_seconds = time_window_minutes * 60

        results = {
            'crossing_trajectories': [],
            'overtaking_trajectories': []
        }

        # 处理交叉避让场景
        if self.crossing_scenes:
            print("处理交叉避让场景...")
            crossing_trajectories = self._extract_trajectories_from_scenes(
                self.crossing_scenes, 'crossing', time_window_seconds
            )
            results['crossing_trajectories'] = crossing_trajectories

        # 处理追越避让场景
        if self.overtaking_scenes:
            print("处理追越避让场景...")
            overtaking_trajectories = self._extract_trajectories_from_scenes(
                self.overtaking_scenes, 'overtaking', time_window_seconds
            )
            results['overtaking_trajectories'] = overtaking_trajectories

        return results

    def _extract_trajectories_from_scenes(self, scenes, scene_type, time_window_seconds):
        """
        从场景列表中提取轨迹

        Args:
            scenes: 场景列表
            scene_type: 场景类型 ('crossing' 或 'overtaking')
            time_window_seconds: 时间窗口（秒）

        Returns:
            list: 轨迹数据列表
        """
        trajectories = []

        for i, scene in enumerate(tqdm(scenes, desc=f"提取{scene_type}轨迹")):
            try:
                # 解析场景数据
                maneuvering_ship = scene[0]  # 机动船信息
                other_ship = scene[1]  # 非机动船信息

                encounter_time = maneuvering_ship['time_point']

                # 计算时间范围
                start_time = encounter_time - time_window_seconds
                end_time = encounter_time + time_window_seconds

                # 提取两船的轨迹
                maneuvering_trajectory = self._get_ship_trajectory_in_timerange(
                    maneuvering_ship['mmsi'], start_time, end_time
                )
                other_trajectory = self._get_ship_trajectory_in_timerange(
                    other_ship['mmsi'], start_time, end_time
                )

                # 构建轨迹数据
                trajectory_data = {
                    'scene_id': f"{scene_type}_{i}",
                    'scene_type': scene_type,
                    'encounter_time': encounter_time,
                    'time_window': {
                        'start': start_time,
                        'end': end_time
                    },
                    'maneuvering_ship': {
                        'mmsi': maneuvering_ship['mmsi'],
                        'encounter_state': maneuvering_ship,
                        'trajectory': maneuvering_trajectory
                    },
                    'other_ship': {
                        'mmsi': other_ship['mmsi'],
                        'encounter_state': other_ship,
                        'trajectory': other_trajectory
                    }
                }

                trajectories.append(trajectory_data)

            except Exception as e:
                print(f"⚠️  处理场景 {i} 时出错: {e}")
                continue

        return trajectories

    def _get_ship_trajectory_in_timerange(self, mmsi, start_time, end_time):
        """
        获取指定船舶在时间范围内的轨迹

        Args:
            mmsi: 船舶MMSI
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            pd.DataFrame: 轨迹数据
        """
        # 筛选指定船舶和时间范围的数据
        trajectory = self.tras_df[
            (self.tras_df['MMSI'] == mmsi) &
            (self.tras_df['PosTime'] >= start_time) &
            (self.tras_df['PosTime'] <= end_time)
            ].copy()

        # 按时间排序
        trajectory = trajectory.sort_values('PosTime')

        return trajectory

    def save_trajectories(self, trajectories, output_dir='result/trajectories'):
        """
        保存提取的轨迹数据

        Args:
            trajectories: 轨迹数据字典
            output_dir: 输出目录
        """
        output_path = Path(output_dir) / self.data_time
        output_path.mkdir(parents=True, exist_ok=True)

        # 保存交叉避让轨迹
        if trajectories['crossing_trajectories']:
            crossing_file = output_path / 'crossing_encounter_trajectories.pkl'
            with open(crossing_file, 'wb') as f:
                pickle.dump(trajectories['crossing_trajectories'], f)
            print(f"✅ 交叉避让轨迹已保存: {crossing_file}")
            print(f"   包含 {len(trajectories['crossing_trajectories'])} 个场景")

        # 保存追越避让轨迹
        if trajectories['overtaking_trajectories']:
            overtaking_file = output_path / 'overtaking_encounter_trajectories.pkl'
            with open(overtaking_file, 'wb') as f:
                pickle.dump(trajectories['overtaking_trajectories'], f)
            print(f"✅ 追越避让轨迹已保存: {overtaking_file}")
            print(f"   包含 {len(trajectories['overtaking_trajectories'])} 个场景")

    def print_summary(self, trajectories):
        """打印提取结果摘要"""
        print(f"\n📊 轨迹提取摘要 ({self.data_time}):")
        print("=" * 50)

        crossing_count = len(trajectories['crossing_trajectories'])
        overtaking_count = len(trajectories['overtaking_trajectories'])
        total_count = crossing_count + overtaking_count

        print(f"总场景数: {total_count}")
        print(f"├─ 交叉避让场景: {crossing_count}")
        print(f"└─ 追越避让场景: {overtaking_count}")

        # 统计轨迹点数
        if crossing_count > 0:
            crossing_points = sum(
                len(traj['maneuvering_ship']['trajectory']) + len(traj['other_ship']['trajectory'])
                for traj in trajectories['crossing_trajectories']
            )
            print(f"\n交叉避让轨迹点数: {crossing_points}")

        if overtaking_count > 0:
            overtaking_points = sum(
                len(traj['maneuvering_ship']['trajectory']) + len(traj['other_ship']['trajectory'])
                for traj in trajectories['overtaking_trajectories']
            )
            print(f"追越避让轨迹点数: {overtaking_points}")


def main():
    """主函数"""
    # 可以处理多个月份的数据
    data_times = ['2024_1', '2024_2', '2024_3']

    for data_time in data_times:
        print(f"\n{'=' * 60}")
        print(f"处理 {data_time} 数据")
        print(f"{'=' * 60}")

        try:
            # 创建提取器
            extractor = EncounterTrajectoryExtractor(data_time)

            # 加载避让场景
            extractor.load_avoidance_scenes()

            # 提取轨迹（时间窗口可调整）
            trajectories = extractor.extract_encounter_trajectories(time_window_minutes=10)

            # 保存结果
            extractor.save_trajectories(trajectories)

            # 打印摘要
            extractor.print_summary(trajectories)

        except Exception as e:
            print(f"❌ 处理 {data_time} 时出错: {e}")
            continue


if __name__ == '__main__':
    main()

#%% md
### 2.批量冲突计算
#%%
import pickle

with open('result/trajectories/2024_1/crossing_encounter_trajectories.pkl', 'rb') as f:
    crossing_trajectories = pickle.load(f)
with open('result/trajectories/2024_1/overtaking_encounter_trajectories.pkl', 'rb') as f:
    overtaking_trajectories = pickle.load(f)
#%%
import pandas as pd
import numpy as np
from shapely.geometry import Point
from shapely.affinity import scale, rotate, translate
from tqdm import tqdm
from methods.trans import Trans

trans = Trans()


def overlap_ratios_shapely(m1, n1, A1, B1, phi1,
                           m2, n2, A2, B2, phi2,
                           circle_resolution=256):
    """
    计算两椭圆交集面积分别占各自面积的比值，使用 Shapely。

    参数:
      - m1,n1,A1,B1,phi1: 椭圆1 的中心、长短半轴及旋转角（度）
      - m2,n2,A2,B2,phi2: 椭圆2 同上
      - circle_resolution: 用于近似圆的分段数，越大结果越精确

    返回:
      (ratio1, ratio2)，其中
        ratio1 = area(intersection) / (π A1 B1)
        ratio2 = area(intersection) / (π A2 B2)
    """
    # 构造单位圆
    unit_circle = Point(0, 0).buffer(1, resolution=circle_resolution)

    # 椭圆1：先缩放，再旋转，最后平移
    e1 = scale(unit_circle, A1, B1)
    e1 = rotate(e1, phi1, origin=(0, 0), use_radians=False)
    e1 = translate(e1, m1, n1)

    # 椭圆2
    e2 = scale(unit_circle, A2, B2)
    e2 = rotate(e2, phi2, origin=(0, 0), use_radians=False)
    e2 = translate(e2, m2, n2)

    # 求交集
    inter = e1.intersection(e2)
    area_inter = inter.area

    # 各自面积
    area1 = np.pi * A1 * B1
    area2 = np.pi * A2 * B2

    return max(area_inter / area1, area_inter / area2)


def get_ab(scence_type, length):
    if scence_type == 'crossing':
        if length <= 100:
            return 271, 192
        else:
            return 375, 210
    else:
        if length <= 100:
            return 180, 85
        else:
            return 290, 120


def conflict_detection(trajectories, scene_type='crossing'):
    Conflicts = []
    for tras in tqdm(trajectories):
        df1 = tras['maneuvering_ship']['trajectory']
        df2 = tras['other_ship']['trajectory']

        a1, b1 = get_ab(scene_type, df1['Length'].values[0])
        a2, b2 = get_ab(scene_type, df2['Length'].values[0])

        common_times = np.intersect1d(df1['PosTime'].values, df2['PosTime'].values)
        df1_aligned = df1[df1['PosTime'].isin(common_times)].reset_index(drop=True)
        df2_aligned = df2[df2['PosTime'].isin(common_times)].reset_index(drop=True)

        X1, Y1 = trans.LonLat2Gauss(df1_aligned['Lon'].values, df1_aligned['Lat'].values)
        X2, Y2 = trans.LonLat2Gauss(df2_aligned['Lon'].values, df2_aligned['Lat'].values)

        Cog1 = df1_aligned['Cog'].values
        Cog2 = df2_aligned['Cog'].values

        conflicts = [overlap_ratios_shapely(X1[i], Y1[i], a1, b1, Cog1[i],
                                            X2[i], Y2[i], a2, b2, Cog2[i])
                     for i in range(len(common_times))]
        Conflicts.append(pd.DataFrame({'PosTime': common_times, 'Conflict': conflicts}))
    return Conflicts


Conflicts_crossing = conflict_detection(crossing_trajectories, scene_type='crossing')
Conflicts_overtaking = conflict_detection(overtaking_trajectories, scene_type='overtaking')