#%% md
### 1.会遇场景轨迹提取
#%%
"""
加载crossing_avoidance_scenes.pkl和overtaking_avoidance_scenes.pkl
提取每种场景中会遇船舶在会遇时间范围内的轨迹

数据结构说明：
- 每个避让场景：[机动船信息, 非机动船信息]
- 船舶信息字典：{'mmsi': int, 'lon': float, 'lat': float, 'cog': float, 'sog': float,
                'length': float, 'width': float, 'time_point': int}
"""

import pickle
import pandas as pd
import numpy as np
from pathlib import Path
from collections import defaultdict
from tqdm import tqdm


class EncounterTrajectoryExtractor:
    """会遇场景轨迹提取器"""

    def __init__(self, data_time='2024_1'):
        """
        初始化轨迹提取器

        Args:
            data_time: 数据时间标识，如 '2024_1', '2024_2', '2024_3'
        """
        self.data_time = data_time
        self.crossing_scenes = []
        self.overtaking_scenes = []
        self.tras_df = None

        # 加载基础数据
        self._load_basic_data()

    def _load_basic_data(self):
        """加载基础轨迹数据"""
        print(f"正在加载 {self.data_time} 的基础数据...")

        # 加载轨迹数据
        tras_file = f'data/tras_{self.data_time}_inter.parquet'
        if Path(tras_file).exists():
            self.tras_df = pd.read_parquet(tras_file)
            print(f"✅ 已加载轨迹数据: {len(self.tras_df)} 条记录")
        else:
            raise FileNotFoundError(f"未找到轨迹数据文件: {tras_file}")

    def load_avoidance_scenes(self):
        """加载避让场景数据"""
        print(f"正在加载 {self.data_time} 的避让场景...")

        # 加载交叉避让场景
        crossing_file = f'result/{self.data_time}/crossing_avoidance_scenes.pkl'
        if Path(crossing_file).exists():
            with open(crossing_file, 'rb') as f:
                self.crossing_scenes = pickle.load(f)
            print(f"✅ 已加载交叉避让场景: {len(self.crossing_scenes)} 个")
        else:
            print(f"⚠️  未找到交叉避让场景文件: {crossing_file}")

        # 加载追越避让场景
        overtaking_file = f'result/{self.data_time}/overtaking_avoidance_scenes.pkl'
        if Path(overtaking_file).exists():
            with open(overtaking_file, 'rb') as f:
                self.overtaking_scenes = pickle.load(f)
            print(f"✅ 已加载追越避让场景: {len(self.overtaking_scenes)} 个")
        else:
            print(f"⚠️  未找到追越避让场景文件: {overtaking_file}")

    def extract_encounter_trajectories(self, time_window_minutes=5):
        """
        提取会遇船舶在会遇时间范围内的轨迹

        Args:
            time_window_minutes: 时间窗口大小（分钟），在会遇时刻前后各扩展的时间

        Returns:
            dict: 包含交叉和追越场景轨迹的字典
        """
        print(f"正在提取会遇轨迹（时间窗口: ±{time_window_minutes}分钟）...")

        # 时间窗口转换为秒
        time_window_seconds = time_window_minutes * 60

        results = {
            'crossing_trajectories': [],
            'overtaking_trajectories': []
        }

        # 处理交叉避让场景
        if self.crossing_scenes:
            print("处理交叉避让场景...")
            crossing_trajectories = self._extract_trajectories_from_scenes(
                self.crossing_scenes, 'crossing', time_window_seconds
            )
            results['crossing_trajectories'] = crossing_trajectories

        # 处理追越避让场景
        if self.overtaking_scenes:
            print("处理追越避让场景...")
            overtaking_trajectories = self._extract_trajectories_from_scenes(
                self.overtaking_scenes, 'overtaking', time_window_seconds
            )
            results['overtaking_trajectories'] = overtaking_trajectories

        return results

    def _extract_trajectories_from_scenes(self, scenes, scene_type, time_window_seconds):
        """
        从场景列表中提取轨迹

        Args:
            scenes: 场景列表
            scene_type: 场景类型 ('crossing' 或 'overtaking')
            time_window_seconds: 时间窗口（秒）

        Returns:
            list: 轨迹数据列表
        """
        trajectories = []

        for i, scene in enumerate(tqdm(scenes, desc=f"提取{scene_type}轨迹")):
            try:
                # 解析场景数据
                maneuvering_ship = scene[0]  # 机动船信息
                other_ship = scene[1]  # 非机动船信息

                encounter_time = maneuvering_ship['time_point']

                # 计算时间范围
                start_time = encounter_time - time_window_seconds
                end_time = encounter_time + time_window_seconds

                # 提取两船的轨迹
                maneuvering_trajectory = self._get_ship_trajectory_in_timerange(
                    maneuvering_ship['mmsi'], start_time, end_time
                )
                other_trajectory = self._get_ship_trajectory_in_timerange(
                    other_ship['mmsi'], start_time, end_time
                )

                # 构建轨迹数据
                trajectory_data = {
                    'scene_id': f"{scene_type}_{i}",
                    'scene_type': scene_type,
                    'encounter_time': encounter_time,
                    'time_window': {
                        'start': start_time,
                        'end': end_time
                    },
                    'maneuvering_ship': {
                        'mmsi': maneuvering_ship['mmsi'],
                        'encounter_state': maneuvering_ship,
                        'trajectory': maneuvering_trajectory
                    },
                    'other_ship': {
                        'mmsi': other_ship['mmsi'],
                        'encounter_state': other_ship,
                        'trajectory': other_trajectory
                    }
                }

                trajectories.append(trajectory_data)

            except Exception as e:
                print(f"⚠️  处理场景 {i} 时出错: {e}")
                continue

        return trajectories

    def _get_ship_trajectory_in_timerange(self, mmsi, start_time, end_time):
        """
        获取指定船舶在时间范围内的轨迹

        Args:
            mmsi: 船舶MMSI
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            pd.DataFrame: 轨迹数据
        """
        # 筛选指定船舶和时间范围的数据
        trajectory = self.tras_df[
            (self.tras_df['MMSI'] == mmsi) &
            (self.tras_df['PosTime'] >= start_time) &
            (self.tras_df['PosTime'] <= end_time)
            ].copy()

        # 按时间排序
        trajectory = trajectory.sort_values('PosTime')

        return trajectory

    def save_trajectories(self, trajectories, output_dir='result/trajectories'):
        """
        保存提取的轨迹数据

        Args:
            trajectories: 轨迹数据字典
            output_dir: 输出目录
        """
        output_path = Path(output_dir) / self.data_time
        output_path.mkdir(parents=True, exist_ok=True)

        # 保存交叉避让轨迹
        if trajectories['crossing_trajectories']:
            crossing_file = output_path / 'crossing_encounter_trajectories.pkl'
            with open(crossing_file, 'wb') as f:
                pickle.dump(trajectories['crossing_trajectories'], f)
            print(f"✅ 交叉避让轨迹已保存: {crossing_file}")
            print(f"   包含 {len(trajectories['crossing_trajectories'])} 个场景")

        # 保存追越避让轨迹
        if trajectories['overtaking_trajectories']:
            overtaking_file = output_path / 'overtaking_encounter_trajectories.pkl'
            with open(overtaking_file, 'wb') as f:
                pickle.dump(trajectories['overtaking_trajectories'], f)
            print(f"✅ 追越避让轨迹已保存: {overtaking_file}")
            print(f"   包含 {len(trajectories['overtaking_trajectories'])} 个场景")

    def print_summary(self, trajectories):
        """打印提取结果摘要"""
        print(f"\n📊 轨迹提取摘要 ({self.data_time}):")
        print("=" * 50)

        crossing_count = len(trajectories['crossing_trajectories'])
        overtaking_count = len(trajectories['overtaking_trajectories'])
        total_count = crossing_count + overtaking_count

        print(f"总场景数: {total_count}")
        print(f"├─ 交叉避让场景: {crossing_count}")
        print(f"└─ 追越避让场景: {overtaking_count}")

        # 统计轨迹点数
        if crossing_count > 0:
            crossing_points = sum(
                len(traj['maneuvering_ship']['trajectory']) + len(traj['other_ship']['trajectory'])
                for traj in trajectories['crossing_trajectories']
            )
            print(f"\n交叉避让轨迹点数: {crossing_points}")

        if overtaking_count > 0:
            overtaking_points = sum(
                len(traj['maneuvering_ship']['trajectory']) + len(traj['other_ship']['trajectory'])
                for traj in trajectories['overtaking_trajectories']
            )
            print(f"追越避让轨迹点数: {overtaking_points}")


def main():
    """主函数"""
    # 可以处理多个月份的数据
    data_times = ['2024_1', '2024_2', '2024_3']

    for data_time in data_times:
        print(f"\n{'=' * 60}")
        print(f"处理 {data_time} 数据")
        print(f"{'=' * 60}")

        try:
            # 创建提取器
            extractor = EncounterTrajectoryExtractor(data_time)

            # 加载避让场景
            extractor.load_avoidance_scenes()

            # 提取轨迹（时间窗口可调整）
            trajectories = extractor.extract_encounter_trajectories(time_window_minutes=10)

            # 保存结果
            extractor.save_trajectories(trajectories)

            # 打印摘要
            extractor.print_summary(trajectories)

        except Exception as e:
            print(f"❌ 处理 {data_time} 时出错: {e}")
            continue


if __name__ == '__main__':
    main()

#%% md
### 2.批量冲突计算
#%%
import pickle

with open('result/trajectories/2024_1/crossing_encounter_trajectories.pkl', 'rb') as f:
    crossing_trajectories = pickle.load(f)
with open('result/trajectories/2024_1/overtaking_encounter_trajectories.pkl', 'rb') as f:
    overtaking_trajectories = pickle.load(f)
#%%
import pandas as pd
import numpy as np
from shapely.geometry import Point
from shapely.affinity import scale, rotate, translate
from tqdm import tqdm
from methods.trans import Trans

trans = Trans()


def overlap_ratios_shapely(m1, n1, A1, B1, phi1,
                           m2, n2, A2, B2, phi2,
                           circle_resolution=256):
    """
    计算两椭圆交集面积分别占各自面积的比值，使用 Shapely。

    参数:
      - m1,n1,A1,B1,phi1: 椭圆1 的中心、长短半轴及旋转角（度）
      - m2,n2,A2,B2,phi2: 椭圆2 同上
      - circle_resolution: 用于近似圆的分段数，越大结果越精确

    返回:
      (ratio1, ratio2)，其中
        ratio1 = area(intersection) / (π A1 B1)
        ratio2 = area(intersection) / (π A2 B2)
    """
    # 构造单位圆
    unit_circle = Point(0, 0).buffer(1, resolution=circle_resolution)

    # 椭圆1：先缩放，再旋转，最后平移
    e1 = scale(unit_circle, A1, B1)
    e1 = rotate(e1, phi1, origin=(0, 0), use_radians=False)
    e1 = translate(e1, m1, n1)

    # 椭圆2
    e2 = scale(unit_circle, A2, B2)
    e2 = rotate(e2, phi2, origin=(0, 0), use_radians=False)
    e2 = translate(e2, m2, n2)

    # 求交集
    inter = e1.intersection(e2)
    area_inter = inter.area

    # 各自面积
    area1 = np.pi * A1 * B1
    area2 = np.pi * A2 * B2

    return max(area_inter / area1, area_inter / area2)


def get_ab(scence_type, length):
    if scence_type == 'crossing':
        if length <= 100:
            return 271, 192
        else:
            return 375, 210
    else:
        if length <= 100:
            return 180, 85
        else:
            return 290, 120


def conflict_detection(trajectories, scene_type='crossing'):
    Conflicts = []
    for tras in tqdm(trajectories):
        df1 = tras['maneuvering_ship']['trajectory']
        df2 = tras['other_ship']['trajectory']

        a1, b1 = get_ab(scene_type, df1['Length'].values[0])
        a2, b2 = get_ab(scene_type, df2['Length'].values[0])

        common_times = np.intersect1d(df1['PosTime'].values, df2['PosTime'].values)
        df1_aligned = df1[df1['PosTime'].isin(common_times)].reset_index(drop=True)
        df2_aligned = df2[df2['PosTime'].isin(common_times)].reset_index(drop=True)

        X1, Y1 = trans.LonLat2Gauss(df1_aligned['Lon'].values, df1_aligned['Lat'].values)
        X2, Y2 = trans.LonLat2Gauss(df2_aligned['Lon'].values, df2_aligned['Lat'].values)

        Cog1 = df1_aligned['Cog'].values
        Cog2 = df2_aligned['Cog'].values

        conflicts = [overlap_ratios_shapely(X1[i], Y1[i], a1, b1, Cog1[i],
                                            X2[i], Y2[i], a2, b2, Cog2[i])
                     for i in range(len(common_times))]
        Conflicts.append(pd.DataFrame({'PosTime': common_times, 'Conflict': conflicts}))
    return Conflicts


Conflicts_crossing = conflict_detection(crossing_trajectories, scene_type='crossing')
Conflicts_overtaking = conflict_detection(overtaking_trajectories, scene_type='overtaking')

#%% md
### 2.批量冲突计算
#%%
def detect_conflict_pattern(conflicts_list, min_conflict_threshold=0.01,
                           min_sequence_length=5, smoothing_window=3):
    """
    检测符合条件的轨迹：冲突先逐渐增大，到最大值，然后逐渐减小

    参数:
    - conflicts_list: 冲突检测结果列表，每个元素是包含'PosTime'和'Conflict'的DataFrame
    - min_conflict_threshold: 最小冲突阈值，低于此值的冲突被忽略
    - min_sequence_length: 最小序列长度，序列长度小于此值的被忽略
    - smoothing_window: 平滑窗口大小，用于减少噪声影响

    返回:
    - pattern_trajectories: 符合条件的轨迹列表，包含轨迹索引和模式信息
    """
    pattern_trajectories = []

    for traj_idx, conflict_df in enumerate(conflicts_list):
        if len(conflict_df) < min_sequence_length:
            continue

        # 获取冲突值序列
        conflicts = conflict_df['Conflict'].values
        times = conflict_df['PosTime'].values

        # 过滤掉过小的冲突值
        valid_mask = conflicts >= min_conflict_threshold
        if np.sum(valid_mask) < min_sequence_length:
            continue

        valid_conflicts = conflicts[valid_mask]
        valid_times = times[valid_mask]

        # 应用移动平均平滑
        if len(valid_conflicts) >= smoothing_window:
            smoothed_conflicts = np.convolve(valid_conflicts,
                                           np.ones(smoothing_window)/smoothing_window,
                                           mode='valid')
            # 调整时间序列以匹配平滑后的长度
            smoothed_times = valid_times[smoothing_window//2:len(valid_conflicts)-smoothing_window//2+1]
        else:
            smoothed_conflicts = valid_conflicts
            smoothed_times = valid_times

        if len(smoothed_conflicts) < min_sequence_length:
            continue

        # 检测模式：先增大后减小
        pattern_info = analyze_conflict_pattern(smoothed_conflicts, smoothed_times)

        if pattern_info['has_pattern']:
            pattern_trajectories.append({
                'trajectory_index': traj_idx,
                'original_conflicts': conflicts,
                'original_times': times,
                'smoothed_conflicts': smoothed_conflicts,
                'smoothed_times': smoothed_times,
                'pattern_info': pattern_info
            })

    return pattern_trajectories


def analyze_conflict_pattern(conflicts, times):
    """
    分析冲突序列是否符合先增大后减小的模式

    参数:
    - conflicts: 冲突值序列
    - times: 对应的时间序列

    返回:
    - 包含模式分析结果的字典
    """
    if len(conflicts) < 3:
        return {'has_pattern': False, 'reason': 'sequence_too_short'}

    # 找到最大值位置
    max_idx = np.argmax(conflicts)
    max_conflict = conflicts[max_idx]
    max_time = times[max_idx]

    # 检查最大值不能在序列的两端
    if max_idx == 0 or max_idx == len(conflicts) - 1:
        return {'has_pattern': False, 'reason': 'max_at_boundary'}

    # 分析上升段和下降段
    rising_phase = conflicts[:max_idx+1]
    falling_phase = conflicts[max_idx:]

    # 检查上升趋势
    rising_trend = analyze_trend(rising_phase)
    falling_trend = analyze_trend(falling_phase, reverse=True)

    # 判断是否符合模式
    has_rising = rising_trend['is_increasing'] and len(rising_phase) >= 2
    has_falling = falling_trend['is_increasing'] and len(falling_phase) >= 2

    # 计算模式强度
    pattern_strength = calculate_pattern_strength(conflicts, max_idx)

    has_pattern = has_rising and has_falling and pattern_strength > 0.3

    return {
        'has_pattern': has_pattern,
        'max_conflict': max_conflict,
        'max_time': max_time,
        'max_index': max_idx,
        'rising_phase_length': len(rising_phase),
        'falling_phase_length': len(falling_phase),
        'rising_trend': rising_trend,
        'falling_trend': falling_trend,
        'pattern_strength': pattern_strength,
        'total_duration': times[-1] - times[0],
        'reason': 'valid_pattern' if has_pattern else 'weak_pattern'
    }


def analyze_trend(sequence, reverse=False):
    """
    分析序列的趋势

    参数:
    - sequence: 数值序列
    - reverse: 是否分析下降趋势（True表示分析下降趋势）

    返回:
    - 趋势分析结果
    """
    if len(sequence) < 2:
        return {'is_increasing': False, 'trend_strength': 0}

    # 计算相邻点的差值
    diffs = np.diff(sequence)
    if reverse:
        diffs = -diffs  # 反转符号来分析下降趋势

    # 计算上升点的比例
    increasing_count = np.sum(diffs > 0)
    total_count = len(diffs)

    if total_count == 0:
        return {'is_increasing': False, 'trend_strength': 0}

    trend_ratio = increasing_count / total_count

    # 计算趋势强度（考虑变化幅度）
    if increasing_count > 0:
        avg_increase = np.mean(diffs[diffs > 0])
        trend_strength = trend_ratio * avg_increase
    else:
        trend_strength = 0

    return {
        'is_increasing': trend_ratio >= 0.6,  # 至少60%的点呈上升趋势
        'trend_strength': trend_strength,
        'trend_ratio': trend_ratio
    }


def calculate_pattern_strength(conflicts, max_idx):
    """
    计算模式强度

    参数:
    - conflicts: 冲突值序列
    - max_idx: 最大值索引

    返回:
    - 模式强度值 (0-1)
    """
    if len(conflicts) < 3:
        return 0

    max_val = conflicts[max_idx]
    start_val = conflicts[0]
    end_val = conflicts[-1]

    # 计算峰值相对于起始和结束值的高度
    start_height = max_val - start_val if max_val > start_val else 0
    end_height = max_val - end_val if max_val > end_val else 0

    # 避免除零
    if max_val == 0:
        return 0

    # 模式强度 = 平均相对高度
    pattern_strength = (start_height + end_height) / (2 * max_val)

    return min(pattern_strength, 1.0)


def print_pattern_summary(pattern_trajectories, scene_type=''):
    """
    打印模式检测结果摘要

    参数:
    - pattern_trajectories: 检测到的模式轨迹列表
    - scene_type: 场景类型（用于显示）
    """
    print(f"\n=== {scene_type} 冲突模式检测结果 ===")
    print(f"检测到符合条件的轨迹数量: {len(pattern_trajectories)}")

    if len(pattern_trajectories) == 0:
        print("未检测到符合条件的轨迹")
        return

    # 统计信息
    max_conflicts = [traj['pattern_info']['max_conflict'] for traj in pattern_trajectories]
    pattern_strengths = [traj['pattern_info']['pattern_strength'] for traj in pattern_trajectories]
    durations = [traj['pattern_info']['total_duration'] for traj in pattern_trajectories]

    print(f"\n统计信息:")
    print(f"最大冲突值范围: {np.min(max_conflicts):.4f} - {np.max(max_conflicts):.4f}")
    print(f"平均最大冲突值: {np.mean(max_conflicts):.4f}")
    print(f"模式强度范围: {np.min(pattern_strengths):.4f} - {np.max(pattern_strengths):.4f}")
    print(f"平均模式强度: {np.mean(pattern_strengths):.4f}")
    print(f"持续时间范围: {np.min(durations):.0f} - {np.max(durations):.0f} 秒")
    print(f"平均持续时间: {np.mean(durations):.0f} 秒")

    # 显示前几个示例
    print(f"\n前5个轨迹详情:")
    for i, traj in enumerate(pattern_trajectories[:5]):
        info = traj['pattern_info']
        print(f"轨迹 {traj['trajectory_index']}: "
              f"最大冲突={info['max_conflict']:.4f}, "
              f"模式强度={info['pattern_strength']:.4f}, "
              f"持续时间={info['total_duration']:.0f}秒")


# 执行模式检测
print("正在检测交叉场景的冲突模式...")
crossing_patterns = detect_conflict_pattern(Conflicts_crossing)
print_pattern_summary(crossing_patterns, "交叉场景")

print("\n正在检测追越场景的冲突模式...")
overtaking_patterns = detect_conflict_pattern(Conflicts_overtaking)
print_pattern_summary(overtaking_patterns, "追越场景")

#%%
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime

def plot_conflict_patterns(pattern_trajectories, conflicts_list, scene_type='', max_plots=6):
    """
    可视化检测到的冲突模式

    参数:
    - pattern_trajectories: 检测到的模式轨迹列表
    - conflicts_list: 原始冲突数据列表
    - scene_type: 场景类型
    - max_plots: 最大绘图数量
    """
    if len(pattern_trajectories) == 0:
        print(f"没有{scene_type}模式可以绘制")
        return

    # 计算子图布局
    n_plots = min(len(pattern_trajectories), max_plots)
    cols = 3
    rows = (n_plots + cols - 1) // cols

    fig, axes = plt.subplots(rows, cols, figsize=(15, 5*rows))
    if rows == 1:
        axes = axes.reshape(1, -1)
    elif n_plots == 1:
        axes = axes.reshape(1, 1)

    fig.suptitle(f'{scene_type} 冲突模式可视化', fontsize=16, fontweight='bold')

    for i in range(n_plots):
        row = i // cols
        col = i % cols
        ax = axes[row, col] if rows > 1 else axes[col]

        traj = pattern_trajectories[i]
        traj_idx = traj['trajectory_index']

        # 获取原始数据
        original_df = conflicts_list[traj_idx]
        times = original_df['PosTime'].values
        conflicts = original_df['Conflict'].values

        # 转换时间为相对时间（秒）
        relative_times = (times - times[0])

        # 绘制原始冲突曲线
        ax.plot(relative_times, conflicts, 'b-', alpha=0.7, linewidth=1, label='原始数据')

        # 绘制平滑后的数据
        if 'smoothed_conflicts' in traj:
            smoothed_times = (traj['smoothed_times'] - times[0])
            ax.plot(smoothed_times, traj['smoothed_conflicts'], 'r-', linewidth=2, label='平滑数据')

            # 标记最大值点
            info = traj['pattern_info']
            max_time_relative = info['max_time'] - times[0]
            ax.plot(max_time_relative, info['max_conflict'], 'ro', markersize=8, label='最大冲突点')

        # 设置图形属性
        ax.set_xlabel('相对时间 (秒)')
        ax.set_ylabel('冲突值')
        ax.set_title(f'轨迹 {traj_idx}\n'
                    f'最大冲突: {traj["pattern_info"]["max_conflict"]:.4f}\n'
                    f'模式强度: {traj["pattern_info"]["pattern_strength"]:.4f}')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 设置y轴范围
        ax.set_ylim(0, max(conflicts) * 1.1)

    # 隐藏多余的子图
    for i in range(n_plots, rows * cols):
        row = i // cols
        col = i % cols
        if rows > 1:
            axes[row, col].set_visible(False)
        else:
            axes[col].set_visible(False)

    plt.tight_layout()
    plt.show()


def analyze_pattern_statistics(crossing_patterns, overtaking_patterns):
    """
    分析模式统计信息
    """
    print("\n=== 综合模式分析 ===")

    all_patterns = crossing_patterns + overtaking_patterns
    if len(all_patterns) == 0:
        print("未检测到任何符合条件的模式")
        return

    # 提取统计数据
    max_conflicts = [p['pattern_info']['max_conflict'] for p in all_patterns]
    pattern_strengths = [p['pattern_info']['pattern_strength'] for p in all_patterns]
    durations = [p['pattern_info']['total_duration'] for p in all_patterns]
    rising_lengths = [p['pattern_info']['rising_phase_length'] for p in all_patterns]
    falling_lengths = [p['pattern_info']['falling_phase_length'] for p in all_patterns]

    print(f"总计检测到 {len(all_patterns)} 个符合条件的轨迹")
    print(f"其中交叉场景: {len(crossing_patterns)} 个")
    print(f"其中追越场景: {len(overtaking_patterns)} 个")

    print(f"\n详细统计:")
    print(f"最大冲突值: 均值={np.mean(max_conflicts):.4f}, 标准差={np.std(max_conflicts):.4f}")
    print(f"模式强度: 均值={np.mean(pattern_strengths):.4f}, 标准差={np.std(pattern_strengths):.4f}")
    print(f"持续时间: 均值={np.mean(durations):.1f}秒, 标准差={np.std(durations):.1f}秒")
    print(f"上升阶段长度: 均值={np.mean(rising_lengths):.1f}, 标准差={np.std(rising_lengths):.1f}")
    print(f"下降阶段长度: 均值={np.mean(falling_lengths):.1f}, 标准差={np.std(falling_lengths):.1f}")

    # 绘制统计直方图
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('冲突模式统计分布', fontsize=16, fontweight='bold')

    # 最大冲突值分布
    axes[0,0].hist(max_conflicts, bins=20, alpha=0.7, color='blue')
    axes[0,0].set_title('最大冲突值分布')
    axes[0,0].set_xlabel('最大冲突值')
    axes[0,0].set_ylabel('频次')

    # 模式强度分布
    axes[0,1].hist(pattern_strengths, bins=20, alpha=0.7, color='green')
    axes[0,1].set_title('模式强度分布')
    axes[0,1].set_xlabel('模式强度')
    axes[0,1].set_ylabel('频次')

    # 持续时间分布
    axes[0,2].hist(durations, bins=20, alpha=0.7, color='red')
    axes[0,2].set_title('持续时间分布')
    axes[0,2].set_xlabel('持续时间 (秒)')
    axes[0,2].set_ylabel('频次')

    # 上升阶段长度分布
    axes[1,0].hist(rising_lengths, bins=20, alpha=0.7, color='orange')
    axes[1,0].set_title('上升阶段长度分布')
    axes[1,0].set_xlabel('上升阶段长度')
    axes[1,0].set_ylabel('频次')

    # 下降阶段长度分布
    axes[1,1].hist(falling_lengths, bins=20, alpha=0.7, color='purple')
    axes[1,1].set_title('下降阶段长度分布')
    axes[1,1].set_xlabel('下降阶段长度')
    axes[1,1].set_ylabel('频次')

    # 场景类型对比
    scene_counts = [len(crossing_patterns), len(overtaking_patterns)]
    scene_labels = ['交叉场景', '追越场景']
    axes[1,2].bar(scene_labels, scene_counts, color=['skyblue', 'lightcoral'])
    axes[1,2].set_title('场景类型分布')
    axes[1,2].set_ylabel('轨迹数量')

    plt.tight_layout()
    plt.show()


# 执行可视化和统计分析
if len(crossing_patterns) > 0:
    print("\n绘制交叉场景冲突模式...")
    plot_conflict_patterns(crossing_patterns, Conflicts_crossing, "交叉场景")

if len(overtaking_patterns) > 0:
    print("\n绘制追越场景冲突模式...")
    plot_conflict_patterns(overtaking_patterns, Conflicts_overtaking, "追越场景")

# 综合统计分析
analyze_pattern_statistics(crossing_patterns, overtaking_patterns)

#%%
def save_pattern_results(crossing_patterns, overtaking_patterns,
                        crossing_trajectories, overtaking_trajectories,
                        output_file='conflict_pattern_results.pkl'):
    """
    保存模式检测结果

    参数:
    - crossing_patterns: 交叉场景模式结果
    - overtaking_patterns: 追越场景模式结果
    - crossing_trajectories: 原始交叉轨迹数据
    - overtaking_trajectories: 原始追越轨迹数据
    - output_file: 输出文件名
    """
    import pickle

    # 构建完整的结果数据
    results = {
        'crossing_patterns': crossing_patterns,
        'overtaking_patterns': overtaking_patterns,
        'summary': {
            'total_crossing_trajectories': len(crossing_trajectories),
            'total_overtaking_trajectories': len(overtaking_trajectories),
            'detected_crossing_patterns': len(crossing_patterns),
            'detected_overtaking_patterns': len(overtaking_patterns),
            'crossing_detection_rate': len(crossing_patterns) / len(crossing_trajectories) if len(crossing_trajectories) > 0 else 0,
            'overtaking_detection_rate': len(overtaking_patterns) / len(overtaking_trajectories) if len(overtaking_trajectories) > 0 else 0
        },
        'metadata': {
            'detection_parameters': {
                'min_conflict_threshold': 0.01,
                'min_sequence_length': 5,
                'smoothing_window': 3
            },
            'timestamp': pd.Timestamp.now().isoformat()
        }
    }

    # 保存结果
    with open(output_file, 'wb') as f:
        pickle.dump(results, f)

    print(f"\n结果已保存到: {output_file}")
    print(f"检测摘要:")
    print(f"- 交叉场景: {results['summary']['detected_crossing_patterns']}/{results['summary']['total_crossing_trajectories']} "
          f"({results['summary']['crossing_detection_rate']:.2%})")
    print(f"- 追越场景: {results['summary']['detected_overtaking_patterns']}/{results['summary']['total_overtaking_trajectories']} "
          f"({results['summary']['overtaking_detection_rate']:.2%})")


def export_pattern_details(crossing_patterns, overtaking_patterns, output_csv='pattern_details.csv'):
    """
    导出模式详细信息到CSV文件

    参数:
    - crossing_patterns: 交叉场景模式结果
    - overtaking_patterns: 追越场景模式结果
    - output_csv: 输出CSV文件名
    """
    details = []

    # 处理交叉场景
    for pattern in crossing_patterns:
        info = pattern['pattern_info']
        details.append({
            'scene_type': 'crossing',
            'trajectory_index': pattern['trajectory_index'],
            'max_conflict': info['max_conflict'],
            'max_time': info['max_time'],
            'pattern_strength': info['pattern_strength'],
            'total_duration': info['total_duration'],
            'rising_phase_length': info['rising_phase_length'],
            'falling_phase_length': info['falling_phase_length'],
            'rising_trend_ratio': info['rising_trend']['trend_ratio'],
            'falling_trend_ratio': info['falling_trend']['trend_ratio']
        })

    # 处理追越场景
    for pattern in overtaking_patterns:
        info = pattern['pattern_info']
        details.append({
            'scene_type': 'overtaking',
            'trajectory_index': pattern['trajectory_index'],
            'max_conflict': info['max_conflict'],
            'max_time': info['max_time'],
            'pattern_strength': info['pattern_strength'],
            'total_duration': info['total_duration'],
            'rising_phase_length': info['rising_phase_length'],
            'falling_phase_length': info['falling_phase_length'],
            'rising_trend_ratio': info['rising_trend']['trend_ratio'],
            'falling_trend_ratio': info['falling_trend']['trend_ratio']
        })

    # 创建DataFrame并保存
    df_details = pd.DataFrame(details)
    df_details.to_csv(output_csv, index=False)

    print(f"\n详细信息已导出到: {output_csv}")
    print(f"共导出 {len(details)} 条记录")


# 保存检测结果
save_pattern_results(crossing_patterns, overtaking_patterns,
                    crossing_trajectories, overtaking_trajectories)

# 导出详细信息
export_pattern_details(crossing_patterns, overtaking_patterns)

print("\n=== 冲突模式检测完成 ===")
print("检测到的轨迹特征：冲突先逐渐增大，到最大值，然后逐渐减小")
print("这种模式通常表示两船从远距离接近，在最近距离时冲突最大，然后逐渐分离")

#%%
